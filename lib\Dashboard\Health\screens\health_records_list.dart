import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../dialogs/health_record_form_dialog.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Cattle/widgets/health_history_card.dart';
import '../../../services/database/database_helper.dart';
import '../../../services/streams/stream_service.dart';
import '../models/health_record_isar.dart';
import 'dart:async'; // Add this for StreamSubscription
import '../../../utils/message_utils.dart';
import '../../Cattle/screens/cattle_detail_screen.dart';
import '../../widgets/reusable_filter_search.dart';
import '../../widgets/filter_models.dart';
import '../../widgets/filter_utils.dart';

class HealthRecordsList extends StatefulWidget {
  const HealthRecordsList({Key? key}) : super(key: key);

  @override
  State<HealthRecordsList> createState() => _HealthRecordsListState();
}

class _HealthRecordsListState extends State<HealthRecordsList> {
  final TextEditingController _searchController = TextEditingController();
  late final DatabaseHelper _dbHelper;

  List<HealthRecordIsar> _healthRecords = [];
  List<HealthRecordIsar> _filteredHealthRecords = [];
  Map<String, CattleIsar> _cattleMap = {};
  List<dynamic> _animalTypes = [];
  Map<String, String> _animalTypeIdToName = {};
  List<CattleIsar> _filteredCattle = [];
  bool _isLoading = true;

  // Filter state using reusable component
  late FilterState _filterState;

  // Stream subscription
  StreamSubscription<Map<String, dynamic>>? _healthRecordSubscription;

  @override
  void initState() {
    super.initState();
    _dbHelper = DatabaseHelper.instance;
    _filterState = FilterState();
    _loadData();
    _subscribeToHealthRecordUpdates();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _healthRecordSubscription?.cancel();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      setState(() => _isLoading = true);

      // Load animal types using DatabaseHelper
      final animalTypes = await _dbHelper.farmSetupHandler.getAllAnimalTypes();

      // Create maps for efficient lookups
      _animalTypeIdToName = {
        for (var type in animalTypes) type.businessId ?? '': type.name ?? ''
      };

      // Load all cattle using DatabaseHelper
      final allCattle = await _dbHelper.cattleHandler.getAllCattle();
      // Create cattle map with both tagId and businessId as keys for robust lookup
      final cattleMap = <String, CattleIsar>{};
      for (var cattle in allCattle) {
        if (cattle.tagId?.isNotEmpty == true) {
          cattleMap[cattle.tagId!] = cattle;
        }
        if (cattle.businessId?.isNotEmpty == true) {
          cattleMap[cattle.businessId!] = cattle;
        }
      }

      // Get all health records
      final allRecords = await _dbHelper.healthHandler.getAllHealthRecords();

      // Update filtered cattle list
      _filteredCattle = FilterUtils.filterCattle(
        allCattle: allCattle,
        selectedAnimalType: _filterState.selectedAnimalType,
        animalTypeIdToName: _animalTypeIdToName,
        genderFilter: null, // Health module shows all cattle
      );

      if (mounted) {
        setState(() {
          _cattleMap = cattleMap;
          _healthRecords = allRecords;
          _animalTypes = animalTypes;
          _isLoading = false;
        });

        // Apply initial filtering
        _filterRecords();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        if (context.mounted) {
          MessageUtils.showError(context, 'Error loading health records: $e');
        }
      }
    }
  }

  // Subscribe to health record updates for real-time UI updates
  void _subscribeToHealthRecordUpdates() {
    // Use StreamService pattern like breeding module
    final streamService = GetIt.instance<StreamService>();
    _healthRecordSubscription = streamService.healthStream.listen((event) {
      debugPrint('Health record stream event: ${event['action']}');
      if (mounted) {
        _loadData(); // Refresh data when health records change
      }
    }, onError: (error) {
      debugPrint('Error in health record stream: $error');
    });
  }

  void _filterRecords() {
    _filteredHealthRecords = FilterUtils.filterRecords<HealthRecordIsar>(
      records: _healthRecords,
      filterState: _filterState,
      cattleMap: _cattleMap,
      animalTypeIdToName: _animalTypeIdToName,
      getCattleId: (record) => record.cattleId ?? '',
      getRecordDate: (record) => record.date,
      getSearchableFields: (record) => [
        record.condition ?? '',
        record.diagnosis ?? '',
        record.treatment ?? '',
        record.notes ?? '',
      ],
    );

    // Sort records by date (newest first)
    _filteredHealthRecords.sort((a, b) {
      if (a.date == null && b.date == null) return 0;
      if (a.date == null) return 1;
      if (b.date == null) return -1;
      return b.date!.compareTo(a.date!);
    });

    setState(() {});
  }

  Future<void> _confirmDeleteHealthRecord(Map<String, dynamic> record) async {
    final healthRecord = _getRecordFromMap(record);
    final cattle = _cattleMap[healthRecord.cattleId ?? ''];
    final cattleDisplayName = cattle?.name != null && cattle?.tagId != null
        ? '${cattle!.name} (${cattle.tagId})'
        : cattle?.name ?? cattle?.tagId ?? 'Unknown';

    final confirmed = await HealthMessageUtils.showHealthRecordDeleteConfirmation(
      context,
      cattleName: cattleDisplayName,
      recordId: healthRecord.recordId,
      recordType: 'health record',
    );

    if (confirmed == true) {
      await _deleteHealthRecord(healthRecord.recordId ?? '');
    }
  }

  Future<void> _deleteHealthRecord(String id) async {
    try {
      await _dbHelper.healthHandler.deleteHealthRecord(id);
      _loadData();
      if (mounted) {
        MessageUtils.showSuccess(context, HealthMessageUtils.healthRecordDeleted());
      }
    } catch (e) {
      if (mounted) {
        MessageUtils.showError(context, 'Error deleting health record: $e');
      }
    }
  }



  void _onFilterChanged(FilterState newFilterState) {
    setState(() {
      _filterState = newFilterState;

      // Update filtered cattle when animal type changes
      if (_filterState.selectedAnimalType != newFilterState.selectedAnimalType) {
        _filteredCattle = FilterUtils.filterCattle(
          allCattle: _cattleMap.values.toList(),
          selectedAnimalType: newFilterState.selectedAnimalType,
          animalTypeIdToName: _animalTypeIdToName,
          genderFilter: null, // Health module shows all cattle
        );

        // Clear cattle selection if it's no longer valid
        if (!FilterUtils.isCattleIdValid(newFilterState.selectedCattleId, _filteredCattle)) {
          _filterState = _filterState.copyWith(selectedCattleId: 'All');
        }
      }
    });

    _filterRecords();
  }

  void _onSearchChanged(String searchQuery) {
    // This is called immediately when search text changes
    // The actual filtering happens in _onFilterChanged
  }

  void _onClearFilters() {
    setState(() {
      _filterState.clearAll();
      _filteredCattle = FilterUtils.filterCattle(
        allCattle: _cattleMap.values.toList(),
        selectedAnimalType: 'All',
        animalTypeIdToName: _animalTypeIdToName,
        genderFilter: null,
      );
    });
    _filterRecords();
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Health Records'),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor: Colors.white,
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showAddHealthRecordDialog(context);
        },
        backgroundColor: const Color(0xFF2E7D32),
        child: const Icon(Icons.add),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Reusable Filter and Search Component
                ReusableFilterSearch(
                  config: FilterConfig.health,
                  filterState: _filterState,
                  filterData: FilterData(
                    cattleMap: _cattleMap,
                    animalTypes: _animalTypes,
                    animalTypeIdToName: _animalTypeIdToName,
                    filteredCattle: _filteredCattle,
                  ),
                  searchController: _searchController,
                  onFilterChanged: _onFilterChanged,
                  onSearchChanged: _onSearchChanged,
                  onClearFilters: _onClearFilters,
                  totalRecords: _healthRecords.length,
                  filteredRecords: _filteredHealthRecords.length,
                ),
                Expanded(
                  child: _filteredHealthRecords.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.medical_services,
                                size: 64,
                                color: Colors.grey[400],
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'No health records found',
                                style: TextStyle(
                                  fontSize: 18,
                                  color: Colors.grey[600],
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Add a health record to start tracking',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[500],
                                ),
                              ),
                              const SizedBox(height: 24),
                              ElevatedButton.icon(
                                onPressed: () {
                                  _showAddHealthRecordDialog(context);
                                },
                                icon: const Icon(Icons.add),
                                label: const Text('Add Health Record'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFF2E7D32),
                                  foregroundColor: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        )
                      : RefreshIndicator(
                          onRefresh: _loadData,
                          child: SingleChildScrollView(
                            physics: const AlwaysScrollableScrollPhysics(),
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              children: [
                                _buildRecordsList(),
                                // Add padding at the bottom for the FAB
                                const SizedBox(height: 80),
                              ],
                            ),
                          ),
                        ),
                ),
              ],
            ),
    );
  }

  void _showAddHealthRecordDialog(BuildContext context) {
    if (!mounted) return;
    final navigatorContext = context;

    // Get unique cattle list to avoid duplicates from the map
    final uniqueCattle = <String, CattleIsar>{};
    for (var cattle in _cattleMap.values) {
      if (cattle.businessId?.isNotEmpty == true) {
        uniqueCattle[cattle.businessId!] = cattle;
      }
    }

    showDialog(
      context: navigatorContext,
      builder: (context) => HealthRecordFormDialog(
        cattle: uniqueCattle.values.toList(),
        onSave: (record) async {
          Navigator.pop(context);
          await _dbHelper.healthHandler.addOrUpdateHealthRecord(record);
          if (!mounted) return;
          await _loadData();
        },
      ),
    );
  }

  // Navigate to cattle detail screen with health tab (health records sub-tab)
  void _navigateToCattleHealthTab(String cattleId) {
    final cattle = _cattleMap[cattleId];
    if (cattle != null) {
      // Determine correct health tab index based on cattle gender
      // Female cattle: Overview(0), Family Tree(1), Breeding(2), Health(3), Milk(4), Events(5)
      // Male cattle: Overview(0), Family Tree(1), Health(2), Events(3)
      final isFemale = (cattle.gender?.toLowerCase() ?? '') == 'female';
      final healthTabIndex = isFemale ? 3 : 2;

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CattleDetailScreen(
            existingCattle: cattle,
            businessId: cattle.businessId ?? '',
            onCattleUpdated: (updatedCattle) {
              // Handle cattle update if needed
            },
            initialTabIndex: healthTabIndex, // Health tab
            initialHealthTabIndex: 0, // Health Records sub-tab
          ),
        ),
      );
    }
  }

  Widget _buildRecordsList() {
    // Prepare records with cattle name and ID info for display
    final recordsForDisplay = _filteredHealthRecords.map((record) {
      final cattle = _cattleMap[record.cattleId];
      final recordMap = record.toMap();
      recordMap['cattleName'] = cattle?.name ?? 'Unknown Cattle';
      recordMap['cattleId'] = cattle?.tagId ?? record.cattleId ?? 'Unknown';
      return recordMap;
    }).toList();

    return HealthHistoryCard(
      records: recordsForDisplay,
      title: 'Health Records',
      emptyMessage: 'No health records found',
      onEdit: (record) => _editHealthRecord(_getRecordFromMap(record)),
      onDelete: (record) => _confirmDeleteHealthRecord(record),
      onCattleTap: (record) {
        // Navigate to cattle detail screen with health tab
        // Use the actual tagId from the cattle record for navigation
        final healthRecord = _getRecordFromMap(record);
        final cattle = _cattleMap[healthRecord.cattleId ?? ''];
        if (cattle?.tagId != null) {
          _navigateToCattleHealthTab(cattle!.tagId!);
        }
      },
    );
  }

  // Helper method to convert map back to HealthRecordIsar
  HealthRecordIsar _getRecordFromMap(Map<String, dynamic> recordMap) {
    return HealthRecordIsar.fromMap(recordMap);
  }

  // Unused method removed - now using HealthHistoryCard

  // Unused method removed - now using HealthHistoryCard

  void _editHealthRecord(HealthRecordIsar record) {
    final cattle = _cattleMap[record.cattleId ?? ''];
    if (cattle == null) return;

    showDialog(
      context: context,
      builder: (context) => HealthRecordFormDialog(
        cattle: _cattleMap.values.toList(),
        healthRecord: record,
        onSave: (updatedRecord) async {
          Navigator.pop(context);
          await _dbHelper.healthHandler.addOrUpdateHealthRecord(updatedRecord);
          await _loadData();
        },
      ),
    );
  }

  // Unused method removed


}
