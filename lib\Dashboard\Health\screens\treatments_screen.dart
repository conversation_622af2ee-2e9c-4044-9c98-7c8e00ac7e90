import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../dialogs/treatment_form_dialog.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Cattle/widgets/treatment_history_card.dart';
import '../../../services/database/database_helper.dart';
import '../../../services/streams/stream_service.dart';
import '../models/treatment_isar.dart';
import 'dart:async'; // Add this for StreamSubscription
import '../../../utils/message_utils.dart';
import '../../Cattle/screens/cattle_detail_screen.dart';
import 'package:isar/isar.dart';
import '../../widgets/reusable_filter_search.dart';
import '../../widgets/filter_models.dart';
import '../../widgets/filter_utils.dart';

class TreatmentsScreen extends StatefulWidget {
  const TreatmentsScreen({
    Key? key,
  }) : super(key: key);

  @override
  State<TreatmentsScreen> createState() => _TreatmentsScreenState();
}

class _TreatmentsScreenState extends State<TreatmentsScreen> {
  final TextEditingController _searchController = TextEditingController();
  late final DatabaseHelper _dbHelper;

  List<TreatmentIsar> _treatments = [];
  List<TreatmentIsar> _filteredTreatments = [];
  Map<String, CattleIsar> _cattleMap = {};
  List<dynamic> _animalTypes = [];
  Map<String, String> _animalTypeIdToName = {};
  List<CattleIsar> _filteredCattle = [];
  bool _isLoading = true;

  // Filter state using reusable component
  late FilterState _filterState;

  // Stream subscription
  StreamSubscription<Map<String, dynamic>>? _treatmentSubscription;

  void _onFilterChanged(FilterState newFilterState) {
    setState(() {
      _filterState = newFilterState;

      // Update filtered cattle when animal type changes
      if (_filterState.selectedAnimalType != newFilterState.selectedAnimalType) {
        _filteredCattle = FilterUtils.filterCattle(
          allCattle: _cattleMap.values.toList(),
          selectedAnimalType: newFilterState.selectedAnimalType,
          animalTypeIdToName: _animalTypeIdToName,
          genderFilter: null, // Health module shows all cattle
        );

        // Clear cattle selection if it's no longer valid
        if (!FilterUtils.isCattleIdValid(newFilterState.selectedCattleId, _filteredCattle)) {
          _filterState = _filterState.copyWith(selectedCattleId: 'All');
        }
      }
    });

    _filterRecords();
  }

  void _onSearchChanged(String searchQuery) {
    // This is called immediately when search text changes
    // The actual filtering happens in _onFilterChanged
  }

  void _onClearFilters() {
    setState(() {
      _filterState.clearAll();
      _filteredCattle = FilterUtils.filterCattle(
        allCattle: _cattleMap.values.toList(),
        selectedAnimalType: 'All',
        animalTypeIdToName: _animalTypeIdToName,
        genderFilter: null,
      );
    });
    _filterRecords();
  }

  @override
  void initState() {
    super.initState();
    _dbHelper = DatabaseHelper.instance;
    _filterState = FilterState();
    _loadData();
    _subscribeToTreatmentUpdates();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _treatmentSubscription?.cancel();
    super.dispose();
  }

  /// Subscribe to treatment record updates for real-time UI updates
  void _subscribeToTreatmentUpdates() {
    // Use StreamService pattern like breeding module
    final streamService = GetIt.instance<StreamService>();
    _treatmentSubscription = streamService.treatmentStream.listen((event) {
      debugPrint('Treatment record stream event: ${event['action']}');
      if (mounted) {
        _loadData(); // Refresh data when treatment records change
      }
    }, onError: (error) {
      debugPrint('Error in treatment record stream: $error');
    });
  }

  Future<void> _loadData() async {
    try {
      setState(() => _isLoading = true);

      // Load animal types using DatabaseHelper
      final animalTypes = await _dbHelper.farmSetupHandler.getAllAnimalTypes();

      // Create maps for efficient lookups
      _animalTypeIdToName = {
        for (var type in animalTypes) type.businessId ?? '': type.name ?? ''
      };

      // Load all cattle using DatabaseHelper
      final allCattle = await _dbHelper.cattleHandler.getAllCattle();
      // Create cattle map with both tagId and businessId as keys for robust lookup
      final cattleMap = <String, CattleIsar>{};
      for (var cattle in allCattle) {
        if (cattle.tagId?.isNotEmpty == true) {
          cattleMap[cattle.tagId!] = cattle;
        }
        if (cattle.businessId?.isNotEmpty == true) {
          cattleMap[cattle.businessId!] = cattle;
        }
      }

      // Migrate existing treatments to have businessId (one-time operation)
      print('🔵 Running treatment businessId migration...');
      await _dbHelper.healthHandler.migrateTreatmentBusinessIds();

      // Get all treatments
      print('🔵 Loading treatments data...');
      final allTreatments = await _dbHelper.healthHandler.getAllTreatments();
      print('🔵 Loaded ${allTreatments.length} treatments');

      // Debug: Check businessId status of treatments
      int treatmentsWithBusinessId = 0;
      int treatmentsWithoutBusinessId = 0;

      for (var treatment in allTreatments) {
        if (treatment.businessId != null && treatment.businessId!.isNotEmpty) {
          treatmentsWithBusinessId++;
        } else {
          treatmentsWithoutBusinessId++;
          print('⚠️ Treatment without businessId: ID=${treatment.id}, treatment=${treatment.treatment}');
        }
      }

      print('🔵 Treatments with businessId: $treatmentsWithBusinessId');
      print('🔵 Treatments without businessId: $treatmentsWithoutBusinessId');

      // Update filtered cattle list
      _filteredCattle = FilterUtils.filterCattle(
        allCattle: allCattle,
        selectedAnimalType: _filterState.selectedAnimalType,
        animalTypeIdToName: _animalTypeIdToName,
        genderFilter: null, // Health module shows all cattle
      );

      if (mounted) {
        setState(() {
          _cattleMap = cattleMap;
          _treatments = allTreatments;
          _animalTypes = animalTypes;
          _isLoading = false;
        });

        // Apply initial filtering
        _filterRecords();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        if (context.mounted) {
          MessageUtils.showError(context, 'Error loading treatments: $e');
        }
      }
    }
  }

  void _filterRecords() {
    _filteredTreatments = FilterUtils.filterRecords<TreatmentIsar>(
      records: _treatments,
      filterState: _filterState,
      cattleMap: _cattleMap,
      animalTypeIdToName: _animalTypeIdToName,
      getCattleId: (record) => record.cattleId ?? '',
      getRecordDate: (record) => record.date,
      getSearchableFields: (record) => [
        record.treatment ?? '',
        record.condition ?? '',
        record.veterinarian ?? '',
        record.notes ?? '',
      ],
    );

    // Sort records by date (newest first)
    _filteredTreatments.sort((a, b) {
      if (a.date == null && b.date == null) return 0;
      if (a.date == null) return 1;
      if (b.date == null) return -1;
      return b.date!.compareTo(a.date!);
    });

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Treatments',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Get unique cattle list to avoid duplicates from the map
          final uniqueCattle = <String, CattleIsar>{};
          for (var cattle in _cattleMap.values) {
            if (cattle.businessId?.isNotEmpty == true) {
              uniqueCattle[cattle.businessId!] = cattle;
            }
          }

          showDialog<void>(
            context: context,
            builder: (context) => TreatmentFormDialog(
              cattle: uniqueCattle.values.toList(),
              onSave: (treatment) async {
                await _dbHelper.healthHandler.addOrUpdateTreatment(
                  treatment.cattleId ?? '',
                  treatment
                );
                if (context.mounted) {
                  Navigator.pop(context);
                  MessageUtils.showSuccess(context, 'Treatment added successfully');
                }
                // Stream will handle the update automatically
              },
            ),
          );
        },
        backgroundColor: const Color(0xFF2E7D32),
        child: const Icon(Icons.add),
      ),
      body: Column(
        children: [
          // Reusable Filter and Search Component
          ReusableFilterSearch(
            config: FilterConfig.health,
            filterState: _filterState,
            filterData: FilterData(
              cattleMap: _cattleMap,
              animalTypes: _animalTypes,
              animalTypeIdToName: _animalTypeIdToName,
              filteredCattle: _filteredCattle,
            ),
            searchController: _searchController,
            onFilterChanged: _onFilterChanged,
            onSearchChanged: _onSearchChanged,
            onClearFilters: _onClearFilters,
            totalRecords: _treatments.length,
            filteredRecords: _filteredTreatments.length,
          ),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredTreatments.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.medication_outlined,
                              size: 64,
                              color: Colors.grey[500],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'No Treatments Found',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey[700],
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Add a new treatment record',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey[500],
                              ),
                            ),
                            const SizedBox(height: 24),
                            ElevatedButton.icon(
                              onPressed: () {
                                // Get unique cattle list to avoid duplicates from the map
                                final uniqueCattle = <String, CattleIsar>{};
                                for (var cattle in _cattleMap.values) {
                                  if (cattle.businessId?.isNotEmpty == true) {
                                    uniqueCattle[cattle.businessId!] = cattle;
                                  }
                                }

                                showDialog<void>(
                                  context: context,
                                  builder: (context) => TreatmentFormDialog(
                                    cattle: uniqueCattle.values.toList(),
                                    onSave: (treatment) async {
                                      await _dbHelper.healthHandler.addOrUpdateTreatment(
                                        treatment.cattleId ?? '',
                                        treatment
                                      );
                                      if (context.mounted) {
                                        Navigator.pop(context);
                                        MessageUtils.showSuccess(context, 'Treatment added successfully');
                                      }
                                      // Stream will handle the update automatically
                                    },
                                  ),
                                );
                              },
                              icon: const Icon(Icons.add),
                              label: const Text('Add Treatment'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF2E7D32),
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _loadData,
                        child: SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            children: [
                              _buildRecordsList(),
                              // Add padding at the bottom for the FAB
                              const SizedBox(height: 80),
                            ],
                          ),
                        ),
                      ),
          ),
        ],
      ),
    );
  }

  // Navigate to cattle detail screen with health tab (treatments sub-tab)
  void _navigateToCattleHealthTab(String cattleId) {
    final cattle = _cattleMap[cattleId];
    if (cattle != null) {
      // Determine correct health tab index based on cattle gender
      // Female cattle: Overview(0), Family Tree(1), Breeding(2), Health(3), Milk(4), Events(5)
      // Male cattle: Overview(0), Family Tree(1), Health(2), Events(3)
      final isFemale = (cattle.gender?.toLowerCase() ?? '') == 'female';
      final healthTabIndex = isFemale ? 3 : 2;

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CattleDetailScreen(
            existingCattle: cattle,
            businessId: cattle.businessId ?? '',
            onCattleUpdated: (updatedCattle) {
              // Handle cattle update if needed
            },
            initialTabIndex: healthTabIndex, // Health tab
            initialHealthTabIndex: 1, // Treatments sub-tab
          ),
        ),
      );
    }
  }

  Widget _buildRecordsList() {
    // Prepare records with cattle name and ID info for display
    final recordsForDisplay = _filteredTreatments.map((treatment) {
      final cattle = _cattleMap[treatment.cattleId ?? ''];
      // Use toMap() method for consistency
      final treatmentMap = treatment.toMap();
      // Add fields expected by TreatmentHistoryCard for unified form compatibility
      treatmentMap['treatmentName'] = treatment.treatment;
      treatmentMap['name'] = treatment.treatment; // Alias for compatibility
      treatmentMap['startDate'] = treatment.date?.toIso8601String();
      treatmentMap['cattleName'] = cattle?.name ?? 'Unknown Cattle';
      treatmentMap['cattleId'] = cattle?.tagId ?? treatment.cattleId ?? 'Unknown';  // Use cattle tag ID for display, fallback to stored cattleId

      // Debug logging for treatment map creation
      if (treatment.businessId == null || treatment.businessId!.isEmpty) {
        print('⚠️ WARNING: Treatment has null/empty businessId!');
        print('⚠️ Treatment details:');
        print('⚠️   - Database ID: ${treatment.id}');
        print('⚠️   - Business ID: ${treatment.businessId}');
        print('⚠️   - Treatment: ${treatment.treatment}');
        print('⚠️   - Cattle ID: ${treatment.cattleId}');
        print('⚠️   - Date: ${treatment.date}');
      }
      return treatmentMap;
    }).toList();

    return TreatmentHistoryCard(
      records: recordsForDisplay,
      title: 'Treatment Records',
      emptyMessage: 'No treatment records found',
      onEdit: (record) => _editTreatment(_getTreatmentFromMap(record)),
      onDelete: (record) {
        print('🔵 TreatmentHistoryCard onDelete called');
        print('🔵 Record keys: ${record.keys.toList()}');
        print('🔵 Record businessId: ${record['businessId']}');
        print('🔵 Record id: ${record['id']}');
        print('🔵 Full record: $record');

        // Use businessId if available, otherwise use database ID
        final businessId = record['businessId']?.toString();
        final databaseId = record['id']?.toString();

        final idToUse = (businessId != null && businessId.isNotEmpty)
            ? businessId
            : databaseId ?? '';

        print('🔵 Using ID for deletion: "$idToUse" (type: ${businessId != null && businessId.isNotEmpty ? 'businessId' : 'databaseId'})');

        return _confirmDeleteTreatment(record, idToUse);
      },
      onCattleTap: (record) {
        // Navigate to cattle detail screen with health tab
        // Use the actual tagId from the cattle record for navigation
        final treatment = _getTreatmentFromMap(record);
        final cattle = _cattleMap[treatment.cattleId ?? ''];
        if (cattle?.tagId != null) {
          _navigateToCattleHealthTab(cattle!.tagId!);
        }
      },
    );
  }

  // Helper method to convert map back to TreatmentIsar
  TreatmentIsar _getTreatmentFromMap(Map<String, dynamic> treatmentMap) {
    return TreatmentIsar(
      cattleId: treatmentMap['cattleId'],
      treatment: treatmentMap['treatmentName'] ?? treatmentMap['name'],
      condition: treatmentMap['condition'],
      notes: treatmentMap['notes'],
      date: treatmentMap['startDate'] != null ? DateTime.parse(treatmentMap['startDate']) : null,
      followUpDate: treatmentMap['followUpDate'],
      cost: treatmentMap['cost'],
      veterinarian: treatmentMap['veterinarian'],
      outcome: treatmentMap['outcome'],
      status: treatmentMap['status'],
      dosage: treatmentMap['dosage'],
      businessId: treatmentMap['businessId'],
    )..id = treatmentMap['id'] ?? Isar.autoIncrement;
  }

  void _editTreatment(TreatmentIsar treatment) {
    // Get unique cattle list to avoid duplicates from the map
    final uniqueCattle = <String, CattleIsar>{};
    for (var cattle in _cattleMap.values) {
      if (cattle.businessId?.isNotEmpty == true) {
        uniqueCattle[cattle.businessId!] = cattle;
      }
    }

    showDialog<void>(
      context: context,
      builder: (context) => TreatmentFormDialog(
        cattle: uniqueCattle.values.toList(),
        treatment: treatment,
        onSave: (updatedTreatment) async {
          await _dbHelper.healthHandler.addOrUpdateTreatment(
            updatedTreatment.cattleId ?? '',
            updatedTreatment
          );
          if (context.mounted) {
            Navigator.pop(context);
            MessageUtils.showSuccess(context, 'Treatment updated successfully');
          }
          // Stream will handle the update automatically
        },
      ),
    );
  }

  // Unused method removed - now using TreatmentHistoryCard

  Future<void> _confirmDeleteTreatment(Map<String, dynamic> record, String id) async {
    final treatment = _getTreatmentFromMap(record);
    final cattle = _cattleMap[treatment.cattleId ?? ''];
    final cattleDisplayName = cattle?.name != null && cattle?.tagId != null
        ? '${cattle!.name} (${cattle.tagId})'
        : cattle?.name ?? cattle?.tagId ?? 'Unknown';

    final confirmed = await HealthMessageUtils.showTreatmentDeleteConfirmation(
      context,
      cattleName: cattleDisplayName,
      recordId: id,
      treatmentName: treatment.treatment,
    );

    if (confirmed == true) {
      await _deleteTreatment(id);
    }
  }

  Future<void> _deleteTreatment(String id) async {
    print('🔵 TreatmentsScreen._deleteTreatment called with id: "$id"');
    print('🔵 ID type: ${id.runtimeType}');
    print('🔵 ID length: ${id.length}');
    print('🔵 ID isEmpty: ${id.isEmpty}');

    try {
      print('🔵 Calling healthHandler.deleteTreatment...');
      await _dbHelper.healthHandler.deleteTreatment(id);
      print('🟢 Treatment deletion completed successfully');

      if (mounted) {
        print('🔵 Showing success message');
        MessageUtils.showSuccess(context, 'Treatment deleted successfully');
      }
      // Stream will handle the update automatically
    } catch (e, stackTrace) {
      print('🔴 Error in _deleteTreatment:');
      print('🔴 Error type: ${e.runtimeType}');
      print('🔴 Error message: $e');
      print('🔴 Stack trace: $stackTrace');

      if (mounted) {
        print('🔵 Showing error message');
        MessageUtils.showError(context, 'Error deleting treatment: $e');
      }
    }
  }

  // Unused method removed
}
