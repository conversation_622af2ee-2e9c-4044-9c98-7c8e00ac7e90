import 'package:logging/logging.dart';
import '../../../services/database/database_helper.dart';

/// Service for providing intelligent suggestions based on historical health data
class HealthSuggestionsService {
  static final Logger _logger = Logger('HealthSuggestionsService');
  static final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  
  /// Get frequently used conditions from historical data
  static Future<List<String>> getFrequentConditions({String? animalType}) async {
    try {
      final healthRecords = await _dbHelper.healthHandler.getAllHealthRecords();
      final treatments = await _dbHelper.healthHandler.getAllTreatments();
      
      final conditionCounts = <String, int>{};
      
      // Count conditions from health records
      for (final record in healthRecords) {
        final condition = record.diagnosis ?? record.condition;
        if (condition?.isNotEmpty == true) {
          conditionCounts[condition!] = (conditionCounts[condition] ?? 0) + 1;
        }
      }
      
      // Count conditions from treatment records
      for (final treatment in treatments) {
        final condition = treatment.condition;
        if (condition?.isNotEmpty == true) {
          conditionCounts[condition!] = (conditionCounts[condition] ?? 0) + 1;
        }
      }
      
      // Sort by frequency and return top 10
      final sortedConditions = conditionCounts.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));
      
      return sortedConditions.take(10).map((e) => e.key).toList();
    } catch (e) {
      _logger.severe('Error getting frequent conditions: $e');
      return [];
    }
  }
  
  /// Get frequently used treatments from historical data
  static Future<List<String>> getFrequentTreatments({String? condition}) async {
    try {
      final healthRecords = await _dbHelper.healthHandler.getAllHealthRecords();
      final treatments = await _dbHelper.healthHandler.getAllTreatments();
      
      final treatmentCounts = <String, int>{};
      
      // Count treatments from health records
      for (final record in healthRecords) {
        // If condition is specified, only count treatments for that condition
        if (condition != null && 
            record.diagnosis != condition && 
            record.condition != condition) {
          continue;
        }
        
        final treatment = record.treatment;
        if (treatment?.isNotEmpty == true) {
          treatmentCounts[treatment!] = (treatmentCounts[treatment] ?? 0) + 1;
        }
      }
      
      // Count treatments from treatment records
      for (final treatment in treatments) {
        // If condition is specified, only count treatments for that condition
        if (condition != null && treatment.condition != condition) {
          continue;
        }
        
        final treatmentName = treatment.treatment;
        if (treatmentName?.isNotEmpty == true) {
          treatmentCounts[treatmentName!] = (treatmentCounts[treatmentName] ?? 0) + 1;
        }
      }
      
      // Sort by frequency and return top 10
      final sortedTreatments = treatmentCounts.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));
      
      return sortedTreatments.take(10).map((e) => e.key).toList();
    } catch (e) {
      _logger.severe('Error getting frequent treatments: $e');
      return [];
    }
  }
  
  /// Get frequently used medicines from historical data
  static Future<List<String>> getFrequentMedicines({String? treatment}) async {
    try {
      final healthRecords = await _dbHelper.healthHandler.getAllHealthRecords();
      
      final medicineCounts = <String, int>{};
      
      // Count medicines from health records
      for (final record in healthRecords) {
        // If treatment is specified, only count medicines for that treatment
        if (treatment != null && record.treatment != treatment) {
          continue;
        }
        
        final medicine = record.medicine;
        if (medicine?.isNotEmpty == true) {
          medicineCounts[medicine!] = (medicineCounts[medicine] ?? 0) + 1;
        }
      }
      
      // Sort by frequency and return top 10
      final sortedMedicines = medicineCounts.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));
      
      return sortedMedicines.take(10).map((e) => e.key).toList();
    } catch (e) {
      _logger.severe('Error getting frequent medicines: $e');
      return [];
    }
  }
  
  /// Get frequently used vaccines from historical data
  static Future<List<String>> getFrequentVaccines({String? animalType}) async {
    try {
      final vaccinations = await _dbHelper.healthHandler.getAllVaccinations();
      final allCattle = await _dbHelper.cattleHandler.getAllCattle();

      // Create a map of cattle ID to animal type for filtering
      final cattleToAnimalType = <String, String>{};
      for (final cattle in allCattle) {
        if (cattle.tagId != null && cattle.tagId!.isNotEmpty) {
          // Determine animal type from tag ID prefix
          final tagPrefix = cattle.tagId!.substring(0, 1).toUpperCase();
          String detectedAnimalType = 'general';
          switch (tagPrefix) {
            case 'C':
              detectedAnimalType = 'cow';
              break;
            case 'B':
              detectedAnimalType = 'buffalo';
              break;
            case 'G':
              detectedAnimalType = 'goat';
              break;
            case 'S':
              detectedAnimalType = 'sheep';
              break;
            case 'H':
              detectedAnimalType = 'horse';
              break;
          }
          cattleToAnimalType[cattle.tagId!] = detectedAnimalType;
        }
      }

      final vaccineCounts = <String, int>{};

      // Count vaccines from vaccination records, filtering by animal type if specified
      for (final vaccination in vaccinations) {
        final vaccineName = vaccination.vaccineName;
        final cattleId = vaccination.cattleId;

        if (vaccineName?.isNotEmpty == true) {
          // If animal type filter is specified, only count vaccines for that animal type
          if (animalType != null && cattleId != null) {
            final cattleAnimalType = cattleToAnimalType[cattleId];
            if (cattleAnimalType != animalType) {
              continue; // Skip this vaccination as it's not for the specified animal type
            }
          }

          vaccineCounts[vaccineName!] = (vaccineCounts[vaccineName] ?? 0) + 1;
        }
      }

      // Sort by frequency and return top 10
      final sortedVaccines = vaccineCounts.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      return sortedVaccines.take(10).map((e) => e.key).toList();
    } catch (e) {
      _logger.severe('Error getting frequent vaccines: $e');
      return [];
    }
  }
  
  /// Get frequently used veterinarians from historical data
  static Future<List<String>> getFrequentVeterinarians() async {
    try {
      final healthRecords = await _dbHelper.healthHandler.getAllHealthRecords();
      final treatments = await _dbHelper.healthHandler.getAllTreatments();

      final vetCounts = <String, int>{};

      // Count veterinarians from health records
      for (final record in healthRecords) {
        final vet = record.veterinarian;
        if (vet?.isNotEmpty == true) {
          vetCounts[vet!] = (vetCounts[vet] ?? 0) + 1;
        }
      }

      // Count veterinarians from treatment records
      for (final treatment in treatments) {
        final vet = treatment.veterinarian;
        if (vet?.isNotEmpty == true) {
          vetCounts[vet!] = (vetCounts[vet] ?? 0) + 1;
        }
      }

      // Note: Vaccination records don't have veterinarian field in current model

      // Sort by frequency and return top 5
      final sortedVets = vetCounts.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      return sortedVets.take(5).map((e) => e.key).toList();
    } catch (e) {
      _logger.severe('Error getting frequent veterinarians: $e');
      return [];
    }
  }
  
  /// Get suggested treatment for a specific condition based on historical data
  static Future<String?> getSuggestedTreatment(String condition) async {
    try {
      final treatments = await getFrequentTreatments(condition: condition);
      return treatments.isNotEmpty ? treatments.first : null;
    } catch (e) {
      _logger.severe('Error getting suggested treatment: $e');
      return null;
    }
  }
  
  /// Get suggested medicine for a specific treatment based on historical data
  static Future<String?> getSuggestedMedicine(String treatment) async {
    try {
      final medicines = await getFrequentMedicines(treatment: treatment);
      return medicines.isNotEmpty ? medicines.first : null;
    } catch (e) {
      _logger.severe('Error getting suggested medicine: $e');
      return null;
    }
  }
  
  /// Get health statistics for analytics
  static Future<Map<String, dynamic>> getHealthStatistics() async {
    try {
      final healthRecords = await _dbHelper.healthHandler.getAllHealthRecords();
      final treatments = await _dbHelper.healthHandler.getAllTreatments();
      final vaccinations = await _dbHelper.healthHandler.getAllVaccinations();
      
      final conditionCounts = <String, int>{};
      final treatmentCounts = <String, int>{};
      final vaccineCounts = <String, int>{};
      
      // Count conditions
      for (final record in healthRecords) {
        final condition = record.diagnosis ?? record.condition;
        if (condition?.isNotEmpty == true) {
          conditionCounts[condition!] = (conditionCounts[condition] ?? 0) + 1;
        }
      }
      
      for (final treatment in treatments) {
        final condition = treatment.condition;
        if (condition?.isNotEmpty == true) {
          conditionCounts[condition!] = (conditionCounts[condition] ?? 0) + 1;
        }
      }
      
      // Count treatments
      for (final record in healthRecords) {
        final treatment = record.treatment;
        if (treatment?.isNotEmpty == true) {
          treatmentCounts[treatment!] = (treatmentCounts[treatment] ?? 0) + 1;
        }
      }
      
      for (final treatment in treatments) {
        final treatmentName = treatment.treatment;
        if (treatmentName?.isNotEmpty == true) {
          treatmentCounts[treatmentName!] = (treatmentCounts[treatmentName] ?? 0) + 1;
        }
      }
      
      // Count vaccines
      for (final vaccination in vaccinations) {
        final vaccineName = vaccination.vaccineName;
        if (vaccineName?.isNotEmpty == true) {
          vaccineCounts[vaccineName!] = (vaccineCounts[vaccineName] ?? 0) + 1;
        }
      }
      
      return {
        'totalHealthRecords': healthRecords.length,
        'totalTreatments': treatments.length,
        'totalVaccinations': vaccinations.length,
        'topConditions': conditionCounts.entries
            .toList()
            ..sort((a, b) => b.value.compareTo(a.value)),
        'topTreatments': treatmentCounts.entries
            .toList()
            ..sort((a, b) => b.value.compareTo(a.value)),
        'topVaccines': vaccineCounts.entries
            .toList()
            ..sort((a, b) => b.value.compareTo(a.value)),
      };
    } catch (e) {
      _logger.severe('Error getting health statistics: $e');
      return {};
    }
  }
}
